import { FileType, DeviceType, HealthStatus } from '../types/CommonTypes';

/**
 * 健康档案
 */
export interface HealthRecord {
  id: string;
  userId: string;
  type: HealthRecordType;
  title: string;
  description?: string;
  data: HealthData;
  files?: HealthFile[];
  deviceId?: string;
  deviceType?: DeviceType;
  recordDate: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 健康记录类型
 */
export enum HealthRecordType {
  PHYSICAL_EXAM = 'physical_exam',
  BLOOD_TEST = 'blood_test',
  VITAL_SIGNS = 'vital_signs',
  MEDICATION = 'medication',
  SYMPTOM = 'symptom',
  DIAGNOSIS = 'diagnosis',
  TREATMENT = 'treatment',
  OTHER = 'other'
}

/**
 * 健康数据
 */
export interface HealthData {
  // 生命体征
  bloodPressure?: {
    systolic: number;
    diastolic: number;
    unit: 'mmHg';
  };
  heartRate?: {
    value: number;
    unit: 'bpm';
  };
  temperature?: {
    value: number;
    unit: 'celsius' | 'fahrenheit';
  };
  bloodGlucose?: {
    value: number;
    unit: 'mmol/L' | 'mg/dL';
    testType: 'fasting' | 'random' | 'postprandial';
  };
  weight?: {
    value: number;
    unit: 'kg' | 'lb';
  };
  height?: {
    value: number;
    unit: 'cm' | 'inch';
  };
  bmi?: {
    value: number;
  };
  
  // 血液检查
  bloodTest?: {
    hemoglobin?: number;
    whiteBloodCells?: number;
    redBloodCells?: number;
    platelets?: number;
    cholesterol?: number;
    triglycerides?: number;
    glucose?: number;
    [key: string]: any;
  };
  
  // 其他自定义数据
  customData?: Record<string, any>;
}

/**
 * 健康文件
 */
export interface HealthFile {
  id: string;
  name: string;
  type: FileType;
  url: string;
  size: number;
  uploadedAt: string;
}

/**
 * 健康趋势
 */
export interface HealthTrend {
  userId: string;
  type: HealthRecordType;
  dataKey: string;
  period: 'week' | 'month' | 'quarter' | 'year';
  data: HealthTrendPoint[];
  analysis?: string;
  recommendations?: string[];
}

/**
 * 健康趋势数据点
 */
export interface HealthTrendPoint {
  date: string;
  value: number;
  status?: HealthStatus;
  note?: string;
}

/**
 * 创建健康记录参数
 */
export interface CreateHealthRecordParams {
  type: HealthRecordType;
  title: string;
  description?: string;
  data: HealthData;
  files?: File[];
  deviceId?: string;
  recordDate?: string;
}

/**
 * 更新健康记录参数
 */
export interface UpdateHealthRecordParams {
  title?: string;
  description?: string;
  data?: Partial<HealthData>;
  recordDate?: string;
}

/**
 * 健康记录查询参数
 */
export interface HealthRecordQuery {
  userId?: string;
  type?: HealthRecordType;
  startDate?: string;
  endDate?: string;
  keyword?: string;
  page?: number;
  pageSize?: number;
}
