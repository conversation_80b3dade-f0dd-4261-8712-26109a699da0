import { COLORS, SPACING, FONT_SIZES } from '../../constants/AppConstants';
import { AppButton } from '../../components/ui/Button';
import { Router } from '../../utils/Router';

/**
 * 引导页面
 */
@Entry
@Component
struct Onboarding {
  @State currentIndex: number = 0;
  private swiperController: SwiperController = new SwiperController();

  private onboardingData = [
    {
      title: '家人健康，一手掌握',
      subtitle: '智能管理全家人的健康档案\n随时了解每个人的健康状况',
      image: 'app.media.onboarding_1'
    },
    {
      title: '紧急情况，一键通知',
      subtitle: '紧急联系人快速响应\n关键时刻守护家人安全',
      image: 'app.media.onboarding_2'
    },
    {
      title: 'AI智能分析',
      subtitle: '个性化健康建议\n专业的健康管理助手',
      image: 'app.media.onboarding_3'
    }
  ];

  build() {
    Column() {
      // 跳过按钮
      Row() {
        Blank();
        Text('跳过')
          .fontSize(FONT_SIZES.MEDIUM)
          .fontColor(COLORS.TEXT_SECONDARY)
          .padding(SPACING.MEDIUM)
          .onClick(() => {
            Router.toLogin();
          });
      }
      .width('100%')
      .margin({ top: 20 });

      // 轮播内容
      Swiper(this.swiperController) {
        ForEach(this.onboardingData, (item: any) => {
          Column({ space: SPACING.EXTRA_LARGE }) {
            // 插画
            Image($r(item.image))
              .width(280)
              .height(280)
              .objectFit(ImageFit.Contain);

            // 文字内容
            Column({ space: SPACING.MEDIUM }) {
              Text(item.title)
                .fontSize(FONT_SIZES.HEADING)
                .fontWeight(FontWeight.Bold)
                .fontColor(COLORS.TEXT_PRIMARY)
                .textAlign(TextAlign.Center);

              Text(item.subtitle)
                .fontSize(FONT_SIZES.LARGE)
                .fontColor(COLORS.TEXT_SECONDARY)
                .textAlign(TextAlign.Center)
                .lineHeight(24);
            }
            .width('100%')
            .padding({ left: SPACING.EXTRA_LARGE, right: SPACING.EXTRA_LARGE });
          }
          .width('100%')
          .height('100%')
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center);
        });
      }
      .width('100%')
      .layoutWeight(1)
      .indicator(
        new DotIndicator()
          .itemWidth(8)
          .itemHeight(8)
          .selectedItemWidth(16)
          .selectedItemHeight(8)
          .color(COLORS.TEXT_DISABLED)
          .selectedColor(COLORS.PRIMARY)
      )
      .onChange((index: number) => {
        this.currentIndex = index;
      });

      // 底部按钮
      Column({ space: SPACING.MEDIUM }) {
        if (this.currentIndex === this.onboardingData.length - 1) {
          AppButton({
            text: '开始使用',
            type: 'primary',
            fullWidth: true,
            onClick: () => {
              Router.toLogin();
            }
          });
        } else {
          Row({ space: SPACING.MEDIUM }) {
            AppButton({
              text: '上一步',
              type: 'outline',
              onClick: () => {
                if (this.currentIndex > 0) {
                  this.currentIndex--;
                  this.swiperController.showPrevious();
                }
              }
            })
            .layoutWeight(1)
            .enabled(this.currentIndex > 0);

            AppButton({
              text: '下一步',
              type: 'primary',
              onClick: () => {
                if (this.currentIndex < this.onboardingData.length - 1) {
                  this.currentIndex++;
                  this.swiperController.showNext();
                }
              }
            })
            .layoutWeight(1);
          }
          .width('100%');
        }
      }
      .width('100%')
      .padding(SPACING.LARGE)
      .margin({ bottom: 20 });
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND);
  }
}
