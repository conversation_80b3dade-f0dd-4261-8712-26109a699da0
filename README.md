# 家庭健康管理系统 (鸿蒙移动端)

## 项目概述

家庭健康管理系统是一款以家庭为中心、由AI驱动的智能健康管理应用。该应用深度融合小程序设计理念，旨在提供极致便捷、高效直观的健康管理体验。

## 功能特性

### 核心功能模块

1. **首页** - 家庭健康概览
   - 紧急警报系统
   - 快速操作入口
   - 今日健康任务
   - 家庭成员健康状态
   - 健康趋势分析

2. **健康档案** - 全面的健康数据管理
   - 个人信息管理
   - 健康档案中心
   - 个人药箱
   - 综合健康概要
   - 紧急资料卡

3. **健康日历** - 任务和活动管理
   - 月视图日历
   - 任务管理
   - 活动追踪
   - 提醒设置

4. **智能助手** - AI驱动的健康咨询
   - 智能对话
   - 健康分析
   - 个性化建议
   - 语音交互

5. **个人中心** - 用户和系统管理
   - 个人信息
   - 家庭管理
   - 系统设置
   - 帮助反馈

## 技术架构

### 项目结构

```
entry/src/main/ets/
├── components/          # 组件库
│   ├── common/         # 通用组件
│   └── ui/             # UI组件
├── constants/          # 常量配置
├── models/             # 数据模型
├── pages/              # 页面
│   ├── auth/           # 认证相关页面
│   ├── home/           # 首页
│   ├── archive/        # 档案页面
│   ├── calendar/       # 日历页面
│   ├── assistant/      # 智能助手
│   ├── profile/        # 个人中心
│   ├── family/         # 家庭管理
│   └── settings/       # 设置页面
├── services/           # 服务层
├── types/              # 类型定义
└── utils/              # 工具类
```

### 核心技术

- **开发框架**: 鸿蒙 ArkTS
- **UI组件**: 自定义组件库
- **状态管理**: ArkTS 状态管理
- **路由管理**: 自定义路由工具
- **主题系统**: 统一的设计系统

## 设计理念

### UI/UX设计

- **设计风格**: 现代、简洁、专业
- **色彩方案**: 以绿色为主色调，体现健康主题
- **布局方式**: 底部导航栏 + 卡片式布局
- **交互设计**: 符合移动端操作习惯

### 移动端优化

- **单手操作**: 底部导航栏设计
- **手势交互**: 支持滑动、点击等手势
- **响应式设计**: 适配不同屏幕尺寸
- **性能优化**: 组件复用和懒加载

## 开发指南

### 环境要求

- DevEco Studio 4.0+
- HarmonyOS SDK API 9+
- Node.js 16+

### 安装和运行

1. 克隆项目到本地
2. 使用 DevEco Studio 打开项目
3. 配置开发环境和SDK
4. 连接设备或启动模拟器
5. 点击运行按钮启动应用

### 开发规范

#### 代码规范

- 使用 TypeScript 严格模式
- 遵循 ArkTS 编码规范
- 组件命名使用 PascalCase
- 文件命名使用 PascalCase

#### 目录规范

- 页面文件放在 `pages` 目录下
- 组件文件放在 `components` 目录下
- 工具类放在 `utils` 目录下
- 常量配置放在 `constants` 目录下

#### 组件开发

- 使用 `@Component` 装饰器
- 实现组件的复用性
- 遵循单一职责原则
- 提供清晰的属性接口

## 数据模型

### 核心实体

- **User**: 用户信息
- **Family**: 家庭信息
- **HealthRecord**: 健康记录
- **Task**: 健康任务
- **Activity**: 健康活动

### 数据流

1. 用户操作 → 页面组件
2. 页面组件 → 服务层
3. 服务层 → 数据模型
4. 数据模型 → 本地存储/远程API

## 部署说明

### 构建配置

- 开发环境: `hvigor --mode development`
- 生产环境: `hvigor --mode production`

### 发布流程

1. 代码审查和测试
2. 版本号更新
3. 构建生产包
4. 应用签名
5. 上传应用市场

## 后续开发计划

### 待实现功能

- [ ] 完善档案模块的子页面
- [ ] 实现日历模块的详细功能
- [ ] 开发智能助手的AI集成
- [ ] 完善个人中心的设置功能
- [ ] 添加数据同步功能
- [ ] 实现推送通知
- [ ] 集成健康设备
- [ ] 添加数据分析图表

### 优化方向

- 性能优化
- 用户体验改进
- 安全性增强
- 国际化支持
- 无障碍功能

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request
5. 代码审查和合并

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目地址: [项目仓库地址]
