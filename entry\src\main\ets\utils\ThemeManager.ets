import { COLORS, FONT_SIZES, SPACING, BORDER_RADIUS } from '../constants/AppConstants';

/**
 * 主题管理器
 */
export class ThemeManager {
  private static instance: ThemeManager;
  private currentTheme: ThemeMode = ThemeMode.LIGHT;

  private constructor() {}

  static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  /**
   * 设置主题模式
   */
  setTheme(theme: ThemeMode): void {
    this.currentTheme = theme;
    // TODO: 保存到本地存储
    // TODO: 通知组件更新
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme(): ThemeMode {
    return this.currentTheme;
  }

  /**
   * 获取主题颜色
   */
  getColors(): ThemeColors {
    return this.currentTheme === ThemeMode.DARK ? DARK_COLORS : LIGHT_COLORS;
  }

  /**
   * 是否为深色主题
   */
  isDarkMode(): boolean {
    return this.currentTheme === ThemeMode.DARK;
  }
}

/**
 * 主题模式枚举
 */
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}

/**
 * 主题颜色接口
 */
export interface ThemeColors {
  primary: string;
  primaryLight: string;
  primaryDark: string;
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  background: string;
  surface: string;
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  border: string;
  divider: string;
}

/**
 * 浅色主题颜色
 */
export const LIGHT_COLORS: ThemeColors = {
  primary: '#2E7D32',
  primaryLight: '#4CAF50',
  primaryDark: '#1B5E20',
  secondary: '#1976D2',
  secondaryLight: '#2196F3',
  secondaryDark: '#0D47A1',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  background: '#FAFAFA',
  surface: '#FFFFFF',
  textPrimary: '#212121',
  textSecondary: '#757575',
  textDisabled: '#BDBDBD',
  border: '#E0E0E0',
  divider: '#EEEEEE'
};

/**
 * 深色主题颜色
 */
export const DARK_COLORS: ThemeColors = {
  primary: '#4CAF50',
  primaryLight: '#81C784',
  primaryDark: '#2E7D32',
  secondary: '#2196F3',
  secondaryLight: '#64B5F6',
  secondaryDark: '#1976D2',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  background: '#121212',
  surface: '#1E1E1E',
  textPrimary: '#FFFFFF',
  textSecondary: '#B3B3B3',
  textDisabled: '#666666',
  border: '#333333',
  divider: '#2A2A2A'
};

/**
 * 样式工具类
 */
export class StyleUtils {
  /**
   * 获取卡片样式
   */
  static getCardStyle(theme?: ThemeColors): CardStyle {
    const colors = theme || ThemeManager.getInstance().getColors();
    return {
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.MEDIUM,
      padding: SPACING.MEDIUM,
      shadowColor: colors.textPrimary,
      shadowOpacity: 0.1,
      shadowRadius: 4,
      shadowOffset: { x: 0, y: 2 }
    };
  }

  /**
   * 获取按钮样式
   */
  static getButtonStyle(type: ButtonType, theme?: ThemeColors): ButtonStyle {
    const colors = theme || ThemeManager.getInstance().getColors();
    
    switch (type) {
      case ButtonType.PRIMARY:
        return {
          backgroundColor: colors.primary,
          textColor: '#FFFFFF',
          borderColor: colors.primary
        };
      case ButtonType.SECONDARY:
        return {
          backgroundColor: colors.secondary,
          textColor: '#FFFFFF',
          borderColor: colors.secondary
        };
      case ButtonType.OUTLINE:
        return {
          backgroundColor: 'transparent',
          textColor: colors.primary,
          borderColor: colors.primary
        };
      case ButtonType.TEXT:
        return {
          backgroundColor: 'transparent',
          textColor: colors.primary,
          borderColor: 'transparent'
        };
      default:
        return {
          backgroundColor: colors.primary,
          textColor: '#FFFFFF',
          borderColor: colors.primary
        };
    }
  }

  /**
   * 获取文本样式
   */
  static getTextStyle(variant: TextVariant, theme?: ThemeColors): TextStyle {
    const colors = theme || ThemeManager.getInstance().getColors();
    
    switch (variant) {
      case TextVariant.HEADING:
        return {
          fontSize: FONT_SIZES.HEADING,
          fontWeight: FontWeight.Bold,
          color: colors.textPrimary
        };
      case TextVariant.TITLE:
        return {
          fontSize: FONT_SIZES.TITLE,
          fontWeight: FontWeight.Bold,
          color: colors.textPrimary
        };
      case TextVariant.BODY:
        return {
          fontSize: FONT_SIZES.MEDIUM,
          fontWeight: FontWeight.Normal,
          color: colors.textPrimary
        };
      case TextVariant.CAPTION:
        return {
          fontSize: FONT_SIZES.SMALL,
          fontWeight: FontWeight.Normal,
          color: colors.textSecondary
        };
      default:
        return {
          fontSize: FONT_SIZES.MEDIUM,
          fontWeight: FontWeight.Normal,
          color: colors.textPrimary
        };
    }
  }
}

/**
 * 按钮类型枚举
 */
export enum ButtonType {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  OUTLINE = 'outline',
  TEXT = 'text'
}

/**
 * 文本变体枚举
 */
export enum TextVariant {
  HEADING = 'heading',
  TITLE = 'title',
  BODY = 'body',
  CAPTION = 'caption'
}

/**
 * 样式接口
 */
export interface CardStyle {
  backgroundColor: string;
  borderRadius: number;
  padding: number;
  shadowColor: string;
  shadowOpacity: number;
  shadowRadius: number;
  shadowOffset: { x: number; y: number };
}

export interface ButtonStyle {
  backgroundColor: string;
  textColor: string;
  borderColor: string;
}

export interface TextStyle {
  fontSize: number;
  fontWeight: FontWeight;
  color: string;
}
