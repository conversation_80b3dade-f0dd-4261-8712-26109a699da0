import { COLORS, SPACING, FONT_SIZES } from '../../constants/AppConstants';
import { AppCard } from '../../components/ui/Card';
import { Router } from '../../utils/Router';

/**
 * 档案页面
 */
@Entry
@Component
struct Archive {
  @State currentTabIndex: number = 0;
  private tabsController: TabsController = new TabsController();

  private tabs = [
    { title: '个人信息', route: '/pages/archive/PersonalInfo' },
    { title: '健康中心', route: '/pages/archive/HealthCenter' },
    { title: '个人药箱', route: '/pages/archive/MedicineBox' },
    { title: '健康概要', route: '/pages/archive/HealthOverview' },
    { title: '紧急资料', route: '/pages/archive/EmergencyCard' }
  ];

  build() {
    Column() {
      // 顶部应用栏
      this.TopAppBar();

      // 标签页
      Tabs({ barPosition: BarPosition.Start, controller: this.tabsController }) {
        ForEach(this.tabs, (tab: any, index: number) => {
          TabContent() {
            this.getTabContent(index);
          }
          .tabBar(this.TabBuilder(tab.title, index));
        });
      }
      .vertical(false)
      .scrollable(true)
      .barMode(BarMode.Scrollable)
      .barWidth('100%')
      .barHeight(50)
      .animationDuration(200)
      .onChange((index: number) => {
        this.currentTabIndex = index;
      })
      .layoutWeight(1);
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND);
  }

  @Builder
  TopAppBar() {
    Row() {
      Text('健康档案')
        .fontSize(FONT_SIZES.TITLE)
        .fontWeight(FontWeight.Bold)
        .fontColor(COLORS.TEXT_PRIMARY);

      Blank();

      Image($r('app.media.ic_search'))
        .width(24)
        .height(24)
        .fillColor(COLORS.TEXT_SECONDARY)
        .onClick(() => {
          // TODO: 打开搜索
        });
    }
    .width('100%')
    .height(60)
    .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM })
    .backgroundColor(COLORS.SURFACE);
  }

  @Builder
  TabBuilder(title: string, targetIndex: number) {
    Column() {
      Text(title)
        .fontSize(FONT_SIZES.MEDIUM)
        .fontColor(this.currentTabIndex === targetIndex ? COLORS.PRIMARY : COLORS.TEXT_SECONDARY)
        .fontWeight(this.currentTabIndex === targetIndex ? FontWeight.Medium : FontWeight.Normal);
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center);
  }

  @Builder
  getTabContent(index: number) {
    switch (index) {
      case 0:
        this.PersonalInfoTab();
        break;
      case 1:
        this.HealthCenterTab();
        break;
      case 2:
        this.MedicineBoxTab();
        break;
      case 3:
        this.HealthOverviewTab();
        break;
      case 4:
        this.EmergencyCardTab();
        break;
      default:
        this.PersonalInfoTab();
    }
  }

  @Builder
  PersonalInfoTab() {
    Scroll() {
      Column({ space: SPACING.MEDIUM }) {
        AppCard() {
          Column({ space: SPACING.MEDIUM }) {
            Text('个人基本信息')
              .fontSize(FONT_SIZES.LARGE)
              .fontWeight(FontWeight.Bold)
              .fontColor(COLORS.TEXT_PRIMARY);

            Text('查看和管理个人健康档案信息')
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_SECONDARY);
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start);
        };
      }
      .width('100%')
      .padding(SPACING.MEDIUM);
    }
    .layoutWeight(1);
  }

  @Builder
  HealthCenterTab() {
    Scroll() {
      Column({ space: SPACING.MEDIUM }) {
        AppCard() {
          Column({ space: SPACING.MEDIUM }) {
            Text('健康档案中心')
              .fontSize(FONT_SIZES.LARGE)
              .fontWeight(FontWeight.Bold)
              .fontColor(COLORS.TEXT_PRIMARY);

            Text('集中管理所有健康记录文件')
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_SECONDARY);
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start);
        };
      }
      .width('100%')
      .padding(SPACING.MEDIUM);
    }
    .layoutWeight(1);
  }

  @Builder
  MedicineBoxTab() {
    Scroll() {
      Column({ space: SPACING.MEDIUM }) {
        AppCard() {
          Column({ space: SPACING.MEDIUM }) {
            Text('个人药箱')
              .fontSize(FONT_SIZES.LARGE)
              .fontWeight(FontWeight.Bold)
              .fontColor(COLORS.TEXT_PRIMARY);

            Text('管理个人药品信息和用药记录')
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_SECONDARY);
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start);
        };
      }
      .width('100%')
      .padding(SPACING.MEDIUM);
    }
    .layoutWeight(1);
  }

  @Builder
  HealthOverviewTab() {
    Scroll() {
      Column({ space: SPACING.MEDIUM }) {
        AppCard() {
          Column({ space: SPACING.MEDIUM }) {
            Text('综合健康概要')
              .fontSize(FONT_SIZES.LARGE)
              .fontWeight(FontWeight.Bold)
              .fontColor(COLORS.TEXT_PRIMARY);

            Text('家庭成员整体健康状况分析')
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_SECONDARY);
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start);
        };
      }
      .width('100%')
      .padding(SPACING.MEDIUM);
    }
    .layoutWeight(1);
  }

  @Builder
  EmergencyCardTab() {
    Scroll() {
      Column({ space: SPACING.MEDIUM }) {
        AppCard() {
          Column({ space: SPACING.MEDIUM }) {
            Text('紧急资料卡')
              .fontSize(FONT_SIZES.LARGE)
              .fontWeight(FontWeight.Bold)
              .fontColor(COLORS.TEXT_PRIMARY);

            Text('紧急情况下的关键医疗信息')
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_SECONDARY);
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start);
        };
      }
      .width('100%')
      .padding(SPACING.MEDIUM);
    }
    .layoutWeight(1);
  }
}
