import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../../constants/AppConstants';

/**
 * 卡片组件
 */
@Component
export struct AppCard {
  @Prop padding: number = SPACING.MEDIUM;
  @Prop margin: number = 0;
  @Prop backgroundColor: string | Color = COLORS.SURFACE;
  @Prop borderRadius: number = BORDER_RADIUS.MEDIUM;
  @Prop shadow: boolean = true;
  @Prop clickable: boolean = false;
  @BuilderParam content: () => void;
  onClick?: () => void;

  build() {
    Column() {
      if (this.content) {
        this.content();
      }
    }
    .width('100%')
    .padding(this.padding)
    .margin(this.margin)
    .backgroundColor(this.backgroundColor)
    .borderRadius(this.borderRadius)
    .shadow(this.shadow ? {
      radius: SHADOWS.MEDIUM.shadowRadius,
      color: SHADOWS.MEDIUM.shadowColor,
      offsetX: SHADOWS.MEDIUM.shadowOffset.x,
      offsetY: SHADOWS.MEDIUM.shadowOffset.y
    } : undefined)
    .onClick(() => {
      if (this.clickable && this.onClick) {
        this.onClick();
      }
    });
  }
}

/**
 * 信息卡片组件
 */
@Component
export struct InfoCard {
  @Prop title: string = '';
  @Prop subtitle?: string;
  @Prop value: string = '';
  @Prop unit?: string;
  @Prop icon?: string;
  @Prop iconColor: string | Color = COLORS.PRIMARY;
  @Prop status?: 'normal' | 'warning' | 'error' | 'success';
  @Prop clickable: boolean = false;
  onClick?: () => void;

  private getStatusColor(): string | Color {
    switch (this.status) {
      case 'warning':
        return COLORS.WARNING;
      case 'error':
        return COLORS.ERROR;
      case 'success':
        return COLORS.SUCCESS;
      default:
        return COLORS.PRIMARY;
    }
  }

  build() {
    AppCard({
      clickable: this.clickable,
      onClick: this.onClick
    }) {
      Row({ space: SPACING.MEDIUM }) {
        if (this.icon) {
          Column() {
            Image($r(this.icon))
              .width(24)
              .height(24)
              .fillColor(this.iconColor);
          }
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
          .width(40)
          .height(40)
          .backgroundColor(this.getStatusColor())
          .borderRadius(BORDER_RADIUS.LARGE)
          .opacity(0.1);
        }

        Column({ space: SPACING.SMALL }) {
          Row() {
            Text(this.title)
              .fontSize(14)
              .fontColor(COLORS.TEXT_SECONDARY)
              .fontWeight(FontWeight.Normal);
          }
          .width('100%')
          .justifyContent(FlexAlign.Start);

          if (this.subtitle) {
            Row() {
              Text(this.subtitle)
                .fontSize(12)
                .fontColor(COLORS.TEXT_DISABLED)
                .fontWeight(FontWeight.Normal);
            }
            .width('100%')
            .justifyContent(FlexAlign.Start);
          }

          Row({ space: SPACING.EXTRA_SMALL }) {
            Text(this.value)
              .fontSize(18)
              .fontColor(this.getStatusColor())
              .fontWeight(FontWeight.Bold);

            if (this.unit) {
              Text(this.unit)
                .fontSize(14)
                .fontColor(COLORS.TEXT_SECONDARY)
                .fontWeight(FontWeight.Normal);
            }
          }
          .width('100%')
          .justifyContent(FlexAlign.Start);
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start);

        if (this.clickable) {
          Image($r('app.media.ic_arrow_right'))
            .width(16)
            .height(16)
            .fillColor(COLORS.TEXT_DISABLED);
        }
      }
      .width('100%')
      .alignItems(VerticalAlign.Center);
    };
  }
}

/**
 * 统计卡片组件
 */
@Component
export struct StatCard {
  @Prop title: string = '';
  @Prop value: string | number = '';
  @Prop change?: string;
  @Prop changeType?: 'increase' | 'decrease' | 'neutral';
  @Prop icon?: string;
  @Prop color: string | Color = COLORS.PRIMARY;

  private getChangeColor(): string | Color {
    switch (this.changeType) {
      case 'increase':
        return COLORS.SUCCESS;
      case 'decrease':
        return COLORS.ERROR;
      default:
        return COLORS.TEXT_SECONDARY;
    }
  }

  build() {
    AppCard() {
      Column({ space: SPACING.MEDIUM }) {
        Row() {
          Column({ space: SPACING.SMALL }) {
            Text(this.title)
              .fontSize(14)
              .fontColor(COLORS.TEXT_SECONDARY)
              .fontWeight(FontWeight.Normal);

            Text(this.value.toString())
              .fontSize(24)
              .fontColor(this.color)
              .fontWeight(FontWeight.Bold);

            if (this.change) {
              Text(this.change)
                .fontSize(12)
                .fontColor(this.getChangeColor())
                .fontWeight(FontWeight.Normal);
            }
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1);

          if (this.icon) {
            Image($r(this.icon))
              .width(32)
              .height(32)
              .fillColor(this.color)
              .opacity(0.8);
          }
        }
        .width('100%')
        .alignItems(VerticalAlign.Top);
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start);
    };
  }
}
