{"deviceType": "phone", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29906", "checkEntry": "true", "localPropertiesPath": "D:\\Code\\vscode\\MyFirstApplication\\local.properties", "Path": "D:\\Download\\Deveco\\DevEco Studio\\tools\\node\\", "aceProfilePath": "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "D:\\Code\\vscode\\MyFirstApplication\\build-profile.json5", "watchMode": "true", "appResource": "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\Code\\vscode\\MyFirstApplication\\entry\\src\\main\\ets", "aceSoPath": "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\Code\\vscode\\MyFirstApplication\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\",\"pages/MainTabs\",\"pages/auth/Onboarding\",\"pages/auth/Login\",\"pages/auth/Register\",\"pages/auth/FamilySetup\",\"pages/home/<USER>\",\"pages/archive/Archive\",\"pages/archive/PersonalInfo\",\"pages/archive/HealthCenter\",\"pages/archive/MedicineBox\",\"pages/archive/HealthOverview\",\"pages/archive/EmergencyCard\",\"pages/calendar/Calendar\",\"pages/calendar/TaskDetail\",\"pages/assistant/Assistant\",\"pages/profile/Profile\",\"pages/family/FamilyManagement\",\"pages/settings/Settings\"]}"]}}