/**
 * 应用常量配置
 */
// API配置
export const API_CONFIG = {
    BASE_URL: 'https://api.familyhealth.com',
    TIMEOUT: 30000,
    VERSION: 'v1'
};
// 存储键名
export const STORAGE_KEYS = {
    USER_TOKEN: 'user_token',
    REFRESH_TOKEN: 'refresh_token',
    USER_INFO: 'user_info',
    FAMILY_INFO: 'family_info',
    APP_SETTINGS: 'app_settings',
    THEME_MODE: 'theme_mode',
    LANGUAGE: 'language'
};
// 路由路径
export const ROUTES = {
    // 认证相关
    LOGIN: '/pages/auth/Login',
    REGISTER: '/pages/auth/Register',
    ONBOARDING: '/pages/auth/Onboarding',
    FAMILY_SETUP: '/pages/auth/FamilySetup',
    // 主要页面
    HOME: '/pages/home/<USER>',
    ARCHIVE: '/pages/archive/Archive',
    CALENDAR: '/pages/calendar/Calendar',
    ASSISTANT: '/pages/assistant/Assistant',
    PROFILE: '/pages/profile/Profile',
    // 档案子页面
    PERSONAL_INFO: '/pages/archive/PersonalInfo',
    HEALTH_CENTER: '/pages/archive/HealthCenter',
    MEDICINE_BOX: '/pages/archive/MedicineBox',
    HEALTH_OVERVIEW: '/pages/archive/HealthOverview',
    EMERGENCY_CARD: '/pages/archive/EmergencyCard',
    // 其他页面
    FAMILY_MANAGEMENT: '/pages/family/FamilyManagement',
    SETTINGS: '/pages/settings/Settings',
    TASK_DETAIL: '/pages/calendar/TaskDetail',
    HEALTH_RECORD_DETAIL: '/pages/archive/HealthRecordDetail'
};
// 底部导航配置
export const BOTTOM_TABS = [
    {
        id: 'home',
        title: '首页',
        icon: 'home',
        route: ROUTES.HOME
    },
    {
        id: 'archive',
        title: '档案',
        route: ROUTES.ARCHIVE
    },
    {
        id: 'calendar',
        title: '日历',
        route: ROUTES.CALENDAR
    },
    {
        id: 'assistant',
        title: '助手',
        route: ROUTES.ASSISTANT
    },
    {
        id: 'profile',
        title: '我的',
        route: ROUTES.PROFILE
    }
];
// 主题颜色
export const COLORS = {
    PRIMARY: '#2E7D32',
    PRIMARY_LIGHT: '#4CAF50',
    PRIMARY_DARK: '#1B5E20',
    SECONDARY: '#1976D2',
    SECONDARY_LIGHT: '#2196F3',
    SECONDARY_DARK: '#0D47A1',
    SUCCESS: '#4CAF50',
    WARNING: '#FF9800',
    ERROR: '#F44336',
    INFO: '#2196F3',
    // 背景色
    BACKGROUND: '#FAFAFA',
    SURFACE: '#FFFFFF',
    // 文字颜色
    TEXT_PRIMARY: '#212121',
    TEXT_SECONDARY: '#757575',
    TEXT_DISABLED: '#BDBDBD',
    // 边框颜色
    BORDER: '#E0E0E0',
    DIVIDER: '#EEEEEE'
};
// 字体大小
export const FONT_SIZES = {
    EXTRA_SMALL: 10,
    SMALL: 12,
    MEDIUM: 14,
    LARGE: 16,
    EXTRA_LARGE: 18,
    TITLE: 20,
    HEADING: 24,
    DISPLAY: 32
};
// 间距
export const SPACING = {
    EXTRA_SMALL: 4,
    SMALL: 8,
    MEDIUM: 16,
    LARGE: 24,
    EXTRA_LARGE: 32
};
// 圆角
export const BORDER_RADIUS = {
    SMALL: 4,
    MEDIUM: 8,
    LARGE: 12,
    EXTRA_LARGE: 16,
    ROUND: 50
};
// 阴影
export const SHADOWS = {
    SMALL: {
        shadowColor: '#000000',
        shadowOpacity: 0.1,
        shadowRadius: 2,
        shadowOffset: { x: 0, y: 1 }
    },
    MEDIUM: {
        shadowColor: '#000000',
        shadowOpacity: 0.15,
        shadowRadius: 4,
        shadowOffset: { x: 0, y: 2 }
    },
    LARGE: {
        shadowColor: '#000000',
        shadowOpacity: 0.2,
        shadowRadius: 8,
        shadowOffset: { x: 0, y: 4 }
    }
};
// 动画时长
export const ANIMATION_DURATION = {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500
};
// 分页配置
export const PAGINATION = {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100
};
// 文件上传配置
export const FILE_UPLOAD = {
    MAX_SIZE: 10 * 1024 * 1024,
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
};
// 健康数据正常范围
export const HEALTH_RANGES = {
    BLOOD_PRESSURE: {
        SYSTOLIC: { min: 90, max: 140 },
        DIASTOLIC: { min: 60, max: 90 }
    },
    HEART_RATE: { min: 60, max: 100 },
    TEMPERATURE: { min: 36.1, max: 37.2 },
    BMI: {
        UNDERWEIGHT: 18.5,
        NORMAL: 24.9,
        OVERWEIGHT: 29.9
    }
};
