import { Gender, BloodType, FamilyRole } from '../types/CommonTypes';

/**
 * 用户模型
 */
export interface User {
  id: string;
  username: string;
  nickname: string;
  avatar?: string;
  phone?: string;
  email?: string;
  gender?: Gender;
  birthday?: string;
  bloodType?: BloodType;
  height?: number; // cm
  weight?: number; // kg
  emergencyContact?: EmergencyContact[];
  familyId?: string;
  familyRole?: FamilyRole;
  createdAt: string;
  updatedAt: string;
}

/**
 * 紧急联系人
 */
export interface EmergencyContact {
  id: string;
  name: string;
  relationship: string;
  phone: string;
  isDefault: boolean;
}

/**
 * 用户登录信息
 */
export interface LoginInfo {
  token: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

/**
 * 用户注册参数
 */
export interface RegisterParams {
  username: string;
  password: string;
  nickname: string;
  phone?: string;
  email?: string;
}

/**
 * 用户登录参数
 */
export interface LoginParams {
  username: string;
  password: string;
}

/**
 * 微信登录参数
 */
export interface WechatLoginParams {
  code: string;
  userInfo?: {
    nickname: string;
    avatar: string;
  };
}

/**
 * 用户更新参数
 */
export interface UpdateUserParams {
  nickname?: string;
  avatar?: string;
  phone?: string;
  email?: string;
  gender?: Gender;
  birthday?: string;
  bloodType?: BloodType;
  height?: number;
  weight?: number;
}
