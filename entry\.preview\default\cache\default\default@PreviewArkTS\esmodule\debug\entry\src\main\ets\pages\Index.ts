if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
}
import { Router } from "@normalized:N&&&entry/src/main/ets/utils/Router&";
import { COLORS } from "@normalized:N&&&entry/src/main/ets/constants/AppConstants&";
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    aboutToAppear() {
        // 检查用户登录状态
        this.checkLoginStatus();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(13:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.backgroundColor(COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 启动画面
            Image.create($r('app.media.app_logo'));
            Image.debugLine("entry/src/main/ets/pages/Index.ets(15:7)", "entry");
            // 启动画面
            Image.width(120);
            // 启动画面
            Image.height(120);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('家庭健康管理');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(19:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(COLORS.TEXT_PRIMARY);
            Text.margin({ top: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('守护家人健康，从这里开始');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(25:7)", "entry");
            Text.fontSize(16);
            Text.fontColor(COLORS.TEXT_SECONDARY);
            Text.margin({ top: 10 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    private checkLoginStatus() {
        // 模拟检查登录状态
        setTimeout(() => {
            // TODO: 检查本地存储的登录信息
            const isLoggedIn = false; // 这里应该从本地存储读取
            if (isLoggedIn) {
                Router.replace('/pages/MainTabs');
            }
            else {
                Router.replace('/pages/auth/Onboarding');
            }
        }, 2000);
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.example.myfirstapplication", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
