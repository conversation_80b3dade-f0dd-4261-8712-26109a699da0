import { COLORS, SPACING, FONT_SIZES } from '../../constants/AppConstants';
import { AppCard, InfoCard, StatCard } from '../../components/ui/Card';
import { AppButton } from '../../components/ui/Button';
import { Router } from '../../utils/Router';

/**
 * 首页
 */
@Entry
@Component
struct Home {
  @State userName: string = '张三';
  @State hasEmergencyAlert: boolean = true;
  @State todayTasks: TaskItem[] = [
    { id: '1', title: '服用降压药', time: '08:00', completed: true },
    { id: '2', title: '血压测量', time: '14:00', completed: false },
    { id: '3', title: '晚间散步', time: '19:00', completed: false }
  ];
  @State familyMembers: FamilyMemberItem[] = [
    { id: '1', name: '张三', avatar: 'app.media.avatar_1', status: 'good', lastUpdate: '2小时前' },
    { id: '2', name: '李四', avatar: 'app.media.avatar_2', status: 'warning', lastUpdate: '1天前' },
    { id: '3', name: '王五', avatar: 'app.media.avatar_3', status: 'excellent', lastUpdate: '3小时前' }
  ];

  build() {
    Column() {
      // 顶部应用栏
      this.TopAppBar();

      // 滚动内容
      Scroll() {
        Column({ space: SPACING.MEDIUM }) {
          // 紧急警报
          if (this.hasEmergencyAlert) {
            this.EmergencyAlert();
          }

          // 快速操作
          this.QuickActions();

          // 今日任务
          this.TodayTasks();

          // 家庭健康状态
          this.FamilyHealthStatus();

          // 健康趋势
          this.HealthTrends();
        }
        .width('100%')
        .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM, bottom: SPACING.LARGE });
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off);
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND);
  }

  @Builder
  TopAppBar() {
    Row() {
      // 用户头像和问候
      Row({ space: SPACING.MEDIUM }) {
        Image($r('app.media.avatar_default'))
          .width(40)
          .height(40)
          .borderRadius(20)
          .onClick(() => {
            Router.push('/pages/profile/Profile');
          });

        Column({ space: 2 }) {
          Text(`早上好，${this.userName}`)
            .fontSize(FONT_SIZES.LARGE)
            .fontWeight(FontWeight.Medium)
            .fontColor(COLORS.TEXT_PRIMARY);

          Text('今天是美好的一天')
            .fontSize(FONT_SIZES.SMALL)
            .fontColor(COLORS.TEXT_SECONDARY);
        }
        .alignItems(HorizontalAlign.Start);
      }
      .layoutWeight(1);

      // 通知和搜索
      Row({ space: SPACING.MEDIUM }) {
        Image($r('app.media.ic_search'))
          .width(24)
          .height(24)
          .fillColor(COLORS.TEXT_SECONDARY)
          .onClick(() => {
            // TODO: 打开搜索
          });

        Badge({
          count: 3,
          position: BadgePosition.RightTop,
          style: { badgeSize: 16, badgeColor: COLORS.ERROR }
        }) {
          Image($r('app.media.ic_notification'))
            .width(24)
            .height(24)
            .fillColor(COLORS.TEXT_SECONDARY)
            .onClick(() => {
              // TODO: 打开通知中心
            });
        };
      };
    }
    .width('100%')
    .height(60)
    .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM })
    .backgroundColor(COLORS.SURFACE);
  }

  @Builder
  EmergencyAlert() {
    AppCard({
      backgroundColor: COLORS.ERROR,
      padding: SPACING.MEDIUM
    }) {
      Row({ space: SPACING.MEDIUM }) {
        Image($r('app.media.ic_warning'))
          .width(24)
          .height(24)
          .fillColor(Color.White);

        Column({ space: SPACING.SMALL }) {
          Text('紧急提醒')
            .fontSize(FONT_SIZES.MEDIUM)
            .fontWeight(FontWeight.Bold)
            .fontColor(Color.White);

          Text('李四血压异常，建议立即就医')
            .fontSize(FONT_SIZES.SMALL)
            .fontColor(Color.White);
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start);

        AppButton({
          text: '查看',
          type: 'text',
          onClick: () => {
            Router.toPersonalInfo('2');
          }
        });
      }
      .width('100%')
      .alignItems(VerticalAlign.Center);
    };
  }

  @Builder
  QuickActions() {
    AppCard() {
      Column({ space: SPACING.MEDIUM }) {
        Row() {
          Text('快速操作')
            .fontSize(FONT_SIZES.LARGE)
            .fontWeight(FontWeight.Bold)
            .fontColor(COLORS.TEXT_PRIMARY);

          Blank();
        }
        .width('100%');

        Grid() {
          GridItem() {
            this.QuickActionItem('家庭管理', 'app.media.ic_family', () => {
              Router.toFamilyManagement();
            });
          }

          GridItem() {
            this.QuickActionItem('健康档案', 'app.media.ic_archive', () => {
              Router.toHealthCenter();
            });
          }

          GridItem() {
            this.QuickActionItem('今日任务', 'app.media.ic_task', () => {
              Router.push('/pages/calendar/Calendar');
            });
          }

          GridItem() {
            this.QuickActionItem('AI助手', 'app.media.ic_assistant', () => {
              Router.push('/pages/assistant/Assistant');
            });
          }
        }
        .columnsTemplate('1fr 1fr 1fr 1fr')
        .rowsGap(SPACING.MEDIUM)
        .height(80);
      }
      .width('100%');
    };
  }

  @Builder
  QuickActionItem(title: string, icon: string, onClick: () => void) {
    Column({ space: SPACING.SMALL }) {
      Image($r(icon))
        .width(32)
        .height(32)
        .fillColor(COLORS.PRIMARY);

      Text(title)
        .fontSize(FONT_SIZES.SMALL)
        .fontColor(COLORS.TEXT_PRIMARY)
        .textAlign(TextAlign.Center);
    }
    .width('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(onClick);
  }

  @Builder
  TodayTasks() {
    AppCard() {
      Column({ space: SPACING.MEDIUM }) {
        Row() {
          Text('今日任务')
            .fontSize(FONT_SIZES.LARGE)
            .fontWeight(FontWeight.Bold)
            .fontColor(COLORS.TEXT_PRIMARY);

          Blank();

          Text('查看全部')
            .fontSize(FONT_SIZES.SMALL)
            .fontColor(COLORS.PRIMARY)
            .onClick(() => {
              Router.push('/pages/calendar/Calendar');
            });
        }
        .width('100%');

        ForEach(this.todayTasks.slice(0, 3), (task: TaskItem) => {
          Row({ space: SPACING.MEDIUM }) {
            Checkbox({ name: task.id, group: 'tasks' })
              .select(task.completed)
              .selectedColor(COLORS.PRIMARY)
              .onChange((value: boolean) => {
                task.completed = value;
              });

            Column({ space: 2 }) {
              Text(task.title)
                .fontSize(FONT_SIZES.MEDIUM)
                .fontColor(task.completed ? COLORS.TEXT_DISABLED : COLORS.TEXT_PRIMARY)
                .decoration({
                  type: task.completed ? TextDecorationType.LineThrough : TextDecorationType.None
                });

              Text(task.time)
                .fontSize(FONT_SIZES.SMALL)
                .fontColor(COLORS.TEXT_SECONDARY);
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start);
          }
          .width('100%')
          .padding({ top: SPACING.SMALL, bottom: SPACING.SMALL });
        });
      }
      .width('100%');
    };
  }

  @Builder
  FamilyHealthStatus() {
    AppCard() {
      Column({ space: SPACING.MEDIUM }) {
        Row() {
          Text('家庭健康状态')
            .fontSize(FONT_SIZES.LARGE)
            .fontWeight(FontWeight.Bold)
            .fontColor(COLORS.TEXT_PRIMARY);

          Blank();

          Text('查看详情')
            .fontSize(FONT_SIZES.SMALL)
            .fontColor(COLORS.PRIMARY)
            .onClick(() => {
              Router.push('/pages/archive/Archive');
            });
        }
        .width('100%');

        ForEach(this.familyMembers, (member: FamilyMemberItem) => {
          Row({ space: SPACING.MEDIUM }) {
            Image($r(member.avatar))
              .width(40)
              .height(40)
              .borderRadius(20);

            Column({ space: 2 }) {
              Text(member.name)
                .fontSize(FONT_SIZES.MEDIUM)
                .fontColor(COLORS.TEXT_PRIMARY);

              Text(`最后更新：${member.lastUpdate}`)
                .fontSize(FONT_SIZES.SMALL)
                .fontColor(COLORS.TEXT_SECONDARY);
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start);

            this.HealthStatusBadge(member.status);
          }
          .width('100%')
          .padding({ top: SPACING.SMALL, bottom: SPACING.SMALL })
          .onClick(() => {
            Router.toPersonalInfo(member.id);
          });
        });
      }
      .width('100%');
    };
  }

  @Builder
  HealthStatusBadge(status: string) {
    Text(this.getStatusText(status))
      .fontSize(FONT_SIZES.SMALL)
      .fontColor(Color.White)
      .backgroundColor(this.getStatusColor(status))
      .padding({ left: SPACING.SMALL, right: SPACING.SMALL, top: 2, bottom: 2 })
      .borderRadius(12);
  }

  @Builder
  HealthTrends() {
    AppCard() {
      Column({ space: SPACING.MEDIUM }) {
        Row() {
          Text('本周健康趋势')
            .fontSize(FONT_SIZES.LARGE)
            .fontWeight(FontWeight.Bold)
            .fontColor(COLORS.TEXT_PRIMARY);

          Blank();
        }
        .width('100%');

        Row({ space: SPACING.MEDIUM }) {
          StatCard({
            title: '完成任务',
            value: 12,
            change: '+3',
            changeType: 'increase',
            color: COLORS.SUCCESS
          })
          .layoutWeight(1);

          StatCard({
            title: '健康评分',
            value: 85,
            change: '+2',
            changeType: 'increase',
            color: COLORS.PRIMARY
          })
          .layoutWeight(1);
        }
        .width('100%');
      }
      .width('100%');
    };
  }

  private getStatusText(status: string): string {
    switch (status) {
      case 'excellent': return '优秀';
      case 'good': return '良好';
      case 'warning': return '注意';
      case 'poor': return '较差';
      default: return '未知';
    }
  }

  private getStatusColor(status: string): string | Color {
    switch (status) {
      case 'excellent': return COLORS.SUCCESS;
      case 'good': return COLORS.PRIMARY;
      case 'warning': return COLORS.WARNING;
      case 'poor': return COLORS.ERROR;
      default: return COLORS.TEXT_DISABLED;
    }
  }
}

interface TaskItem {
  id: string;
  title: string;
  time: string;
  completed: boolean;
}

interface FamilyMemberItem {
  id: string;
  name: string;
  avatar: string;
  status: string;
  lastUpdate: string;
}
