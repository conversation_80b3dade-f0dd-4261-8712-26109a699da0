import { COLORS } from '../constants/AppConstants';

/**
 * 主页面标签容器
 */
@Entry
@Component
struct MainTabs {
  @State currentIndex: number = 0;

  private tabsController: TabsController = new TabsController();

  build() {
    Column() {
      Tabs({ barPosition: BarPosition.End, controller: this.tabsController }) {
        TabContent() {
          HomePage();
        }
        .tabBar(this.TabBuilder('首页', 0, $r('app.media.ic_home')));

        TabContent() {
          ArchivePage();
        }
        .tabBar(this.TabBuilder('档案', 1, $r('app.media.ic_archive')));

        TabContent() {
          CalendarPage();
        }
        .tabBar(this.TabBuilder('日历', 2, $r('app.media.ic_calendar')));

        TabContent() {
          AssistantPage();
        }
        .tabBar(this.TabBuilder('助手', 3, $r('app.media.ic_assistant')));

        TabContent() {
          ProfilePage();
        }
        .tabBar(this.TabBuilder('我的', 4, $r('app.media.ic_profile')));
      }
      .vertical(false)
      .scrollable(false)
      .barMode(BarMode.Fixed)
      .barWidth('100%')
      .barHeight(60)
      .animationDuration(200)
      .onChange((index: number) => {
        this.currentIndex = index;
      })
      .backgroundColor(COLORS.BACKGROUND);
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND);
  }

  @Builder
  TabBuilder(title: string, targetIndex: number, icon: Resource) {
    Column({ space: 4 }) {
      Image(icon)
        .width(24)
        .height(24)
        .fillColor(this.currentIndex === targetIndex ? COLORS.PRIMARY : COLORS.TEXT_DISABLED);

      Text(title)
        .fontSize(12)
        .fontColor(this.currentIndex === targetIndex ? COLORS.PRIMARY : COLORS.TEXT_DISABLED)
        .fontWeight(this.currentIndex === targetIndex ? FontWeight.Medium : FontWeight.Normal);
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center);
  }
}

/**
 * 首页组件
 */
@Component
struct HomePage {
  build() {
    Column() {
      Text('首页')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 });

      Text('家庭健康概览')
        .fontSize(16)
        .fontColor(COLORS.TEXT_SECONDARY)
        .margin({ top: 20 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor(COLORS.BACKGROUND);
  }
}

/**
 * 档案页面组件
 */
@Component
struct ArchivePage {
  build() {
    Column() {
      Text('健康档案')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 });

      Text('管理家庭成员健康档案')
        .fontSize(16)
        .fontColor(COLORS.TEXT_SECONDARY)
        .margin({ top: 20 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor(COLORS.BACKGROUND);
  }
}

/**
 * 日历页面组件
 */
@Component
struct CalendarPage {
  build() {
    Column() {
      Text('健康日历')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 });

      Text('管理健康任务和活动')
        .fontSize(16)
        .fontColor(COLORS.TEXT_SECONDARY)
        .margin({ top: 20 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor(COLORS.BACKGROUND);
  }
}

/**
 * 智能助手页面组件
 */
@Component
struct AssistantPage {
  build() {
    Column() {
      Text('智能助手')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 });

      Text('AI健康咨询和分析')
        .fontSize(16)
        .fontColor(COLORS.TEXT_SECONDARY)
        .margin({ top: 20 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor(COLORS.BACKGROUND);
  }
}

/**
 * 个人中心页面组件
 */
@Component
struct ProfilePage {
  build() {
    Column() {
      Text('个人中心')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 50 });

      Text('个人信息和设置')
        .fontSize(16)
        .fontColor(COLORS.TEXT_SECONDARY)
        .margin({ top: 20 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor(COLORS.BACKGROUND);
  }
}
