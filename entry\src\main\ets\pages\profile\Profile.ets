import { COLORS, SPACING, FONT_SIZES } from '../../constants/AppConstants';
import { AppCard } from '../../components/ui/Card';
import { Router } from '../../utils/Router';

/**
 * 个人中心页面
 */
@Entry
@Component
struct Profile {
  @State userInfo = {
    name: '张三',
    avatar: 'app.media.avatar_default',
    phone: '138****8888',
    familyName: '张家大院'
  };

  private menuItems = [
    {
      title: '家庭管理',
      icon: 'app.media.ic_family',
      route: '/pages/family/FamilyManagement',
      description: '管理家庭成员和权限'
    },
    {
      title: '设置',
      icon: 'app.media.ic_settings',
      route: '/pages/settings/Settings',
      description: '个性化配置和隐私设置'
    },
    {
      title: '帮助与反馈',
      icon: 'app.media.ic_help',
      route: '/pages/help/Help',
      description: '常见问题和意见反馈'
    },
    {
      title: '关于我们',
      icon: 'app.media.ic_info',
      route: '/pages/about/About',
      description: '应用信息和版本'
    }
  ];

  build() {
    Column() {
      // 顶部应用栏
      this.TopAppBar();

      // 滚动内容
      Scroll() {
        Column({ space: SPACING.MEDIUM }) {
          // 用户信息卡片
          this.UserInfoCard();

          // 功能菜单
          this.MenuList();

          // 退出登录
          this.LogoutSection();
        }
        .width('100%')
        .padding(SPACING.MEDIUM);
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off);
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND);
  }

  @Builder
  TopAppBar() {
    Row() {
      Text('我的')
        .fontSize(FONT_SIZES.TITLE)
        .fontWeight(FontWeight.Bold)
        .fontColor(COLORS.TEXT_PRIMARY);

      Blank();

      Image($r('app.media.ic_notification'))
        .width(24)
        .height(24)
        .fillColor(COLORS.TEXT_SECONDARY)
        .onClick(() => {
          // TODO: 打开通知中心
        });
    }
    .width('100%')
    .height(60)
    .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM })
    .backgroundColor(COLORS.SURFACE);
  }

  @Builder
  UserInfoCard() {
    AppCard({
      clickable: true,
      onClick: () => {
        // TODO: 跳转到个人信息编辑页面
      }
    }) {
      Row({ space: SPACING.MEDIUM }) {
        Image($r(this.userInfo.avatar))
          .width(60)
          .height(60)
          .borderRadius(30);

        Column({ space: SPACING.SMALL }) {
          Text(this.userInfo.name)
            .fontSize(FONT_SIZES.LARGE)
            .fontWeight(FontWeight.Bold)
            .fontColor(COLORS.TEXT_PRIMARY);

          Text(this.userInfo.phone)
            .fontSize(FONT_SIZES.MEDIUM)
            .fontColor(COLORS.TEXT_SECONDARY);

          Row({ space: SPACING.SMALL }) {
            Image($r('app.media.ic_family'))
              .width(16)
              .height(16)
              .fillColor(COLORS.PRIMARY);

            Text(this.userInfo.familyName)
              .fontSize(FONT_SIZES.SMALL)
              .fontColor(COLORS.PRIMARY);
          };
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start);

        Image($r('app.media.ic_arrow_right'))
          .width(20)
          .height(20)
          .fillColor(COLORS.TEXT_DISABLED);
      }
      .width('100%')
      .alignItems(VerticalAlign.Center);
    };
  }

  @Builder
  MenuList() {
    AppCard() {
      Column() {
        ForEach(this.menuItems, (item: any, index: number) => {
          Column() {
            Row({ space: SPACING.MEDIUM }) {
              Image($r(item.icon))
                .width(24)
                .height(24)
                .fillColor(COLORS.TEXT_SECONDARY);

              Column({ space: 2 }) {
                Text(item.title)
                  .fontSize(FONT_SIZES.MEDIUM)
                  .fontColor(COLORS.TEXT_PRIMARY)
                  .fontWeight(FontWeight.Medium);

                Text(item.description)
                  .fontSize(FONT_SIZES.SMALL)
                  .fontColor(COLORS.TEXT_SECONDARY);
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start);

              Image($r('app.media.ic_arrow_right'))
                .width(16)
                .height(16)
                .fillColor(COLORS.TEXT_DISABLED);
            }
            .width('100%')
            .padding({ top: SPACING.MEDIUM, bottom: SPACING.MEDIUM })
            .alignItems(VerticalAlign.Center)
            .onClick(() => {
              Router.push(item.route);
            });

            if (index < this.menuItems.length - 1) {
              Divider()
                .color(COLORS.DIVIDER)
                .margin({ left: 48 });
            }
          };
        });
      }
      .width('100%');
    };
  }

  @Builder
  LogoutSection() {
    AppCard({
      clickable: true,
      onClick: () => {
        this.showLogoutDialog();
      }
    }) {
      Row() {
        Image($r('app.media.ic_logout'))
          .width(24)
          .height(24)
          .fillColor(COLORS.ERROR);

        Text('退出登录')
          .fontSize(FONT_SIZES.MEDIUM)
          .fontColor(COLORS.ERROR)
          .fontWeight(FontWeight.Medium)
          .margin({ left: SPACING.MEDIUM });

        Blank();
      }
      .width('100%')
      .padding({ top: SPACING.MEDIUM, bottom: SPACING.MEDIUM })
      .alignItems(VerticalAlign.Center);
    };
  }

  private showLogoutDialog() {
    AlertDialog.show({
      title: '退出登录',
      message: '确定要退出当前账号吗？',
      primaryButton: {
        value: '取消',
        action: () => {
          // 取消操作
        }
      },
      secondaryButton: {
        value: '确定',
        fontColor: COLORS.ERROR,
        action: () => {
          this.logout();
        }
      }
    });
  }

  private logout() {
    // TODO: 清除用户数据
    // TODO: 跳转到登录页面
    Router.clear('/pages/auth/Login');
  }
}
