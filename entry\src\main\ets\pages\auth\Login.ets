import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants/AppConstants';
import { AppButton } from '../../components/ui/Button';
import { AppCard } from '../../components/ui/Card';
import { Router } from '../../utils/Router';

/**
 * 登录页面
 */
@Entry
@Component
struct Login {
  @State username: string = '';
  @State password: string = '';
  @State isLoading: boolean = false;
  @State showPassword: boolean = false;

  build() {
    Column() {
      // 顶部Logo和标题
      Column({ space: SPACING.LARGE }) {
        Image($r('app.media.app_logo'))
          .width(80)
          .height(80)
          .margin({ top: 60 });

        Column({ space: SPACING.SMALL }) {
          Text('家庭健康管理')
            .fontSize(FONT_SIZES.HEADING)
            .fontWeight(FontWeight.Bold)
            .fontColor(COLORS.TEXT_PRIMARY);

          Text('守护家人健康，从这里开始')
            .fontSize(FONT_SIZES.MEDIUM)
            .fontColor(COLORS.TEXT_SECONDARY);
        }
        .alignItems(HorizontalAlign.Center);
      }
      .width('100%')
      .alignItems(HorizontalAlign.Center)
      .margin({ bottom: SPACING.EXTRA_LARGE });

      // 登录表单
      AppCard({
        padding: SPACING.LARGE,
        margin: SPACING.MEDIUM
      }) {
        Column({ space: SPACING.LARGE }) {
          // 用户名输入
          Column({ space: SPACING.SMALL }) {
            Text('用户名/手机号')
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_PRIMARY)
              .alignSelf(ItemAlign.Start);

            TextInput({ placeholder: '请输入用户名或手机号' })
              .height(48)
              .borderRadius(BORDER_RADIUS.MEDIUM)
              .backgroundColor(COLORS.BACKGROUND)
              .fontSize(FONT_SIZES.MEDIUM)
              .onChange((value: string) => {
                this.username = value;
              });
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start);

          // 密码输入
          Column({ space: SPACING.SMALL }) {
            Text('密码')
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_PRIMARY)
              .alignSelf(ItemAlign.Start);

            Row() {
              TextInput({ placeholder: '请输入密码' })
                .height(48)
                .borderRadius(BORDER_RADIUS.MEDIUM)
                .backgroundColor(COLORS.BACKGROUND)
                .fontSize(FONT_SIZES.MEDIUM)
                .type(this.showPassword ? InputType.Normal : InputType.Password)
                .layoutWeight(1)
                .onChange((value: string) => {
                  this.password = value;
                });

              Image($r(this.showPassword ? 'app.media.ic_eye_off' : 'app.media.ic_eye'))
                .width(20)
                .height(20)
                .fillColor(COLORS.TEXT_DISABLED)
                .margin({ left: SPACING.SMALL })
                .onClick(() => {
                  this.showPassword = !this.showPassword;
                });
            }
            .width('100%')
            .alignItems(VerticalAlign.Center);
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start);

          // 忘记密码
          Row() {
            Blank();
            Text('忘记密码？')
              .fontSize(FONT_SIZES.SMALL)
              .fontColor(COLORS.PRIMARY)
              .onClick(() => {
                // TODO: 跳转到忘记密码页面
              });
          }
          .width('100%');

          // 登录按钮
          AppButton({
            text: '登录',
            type: 'primary',
            fullWidth: true,
            loading: this.isLoading,
            onClick: () => {
              this.handleLogin();
            }
          });

          // 微信登录
          Column({ space: SPACING.MEDIUM }) {
            Row() {
              Divider()
                .layoutWeight(1)
                .color(COLORS.BORDER);

              Text('或')
                .fontSize(FONT_SIZES.SMALL)
                .fontColor(COLORS.TEXT_DISABLED)
                .margin({ left: SPACING.MEDIUM, right: SPACING.MEDIUM });

              Divider()
                .layoutWeight(1)
                .color(COLORS.BORDER);
            }
            .width('100%');

            AppButton({
              text: '微信一键登录',
              type: 'outline',
              fullWidth: true,
              icon: 'app.media.ic_wechat',
              onClick: () => {
                this.handleWechatLogin();
              }
            });
          }
          .width('100%');
        }
        .width('100%');
      };

      Blank();

      // 注册链接
      Row({ space: SPACING.SMALL }) {
        Text('还没有账号？')
          .fontSize(FONT_SIZES.MEDIUM)
          .fontColor(COLORS.TEXT_SECONDARY);

        Text('立即注册')
          .fontSize(FONT_SIZES.MEDIUM)
          .fontColor(COLORS.PRIMARY)
          .fontWeight(FontWeight.Medium)
          .onClick(() => {
            Router.push('/pages/auth/Register');
          });
      }
      .margin({ bottom: 30 });
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND)
    .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM });
  }

  private handleLogin() {
    if (!this.username || !this.password) {
      // TODO: 显示错误提示
      return;
    }

    this.isLoading = true;

    // TODO: 调用登录API
    setTimeout(() => {
      this.isLoading = false;
      // 登录成功后跳转到主页面
      Router.clear('/pages/MainTabs');
    }, 2000);
  }

  private handleWechatLogin() {
    // TODO: 调用微信登录API
    Router.clear('/pages/MainTabs');
  }
}
