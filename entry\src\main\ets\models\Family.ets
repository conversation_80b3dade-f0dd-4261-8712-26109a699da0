import { FamilyRole } from '../types/CommonTypes';
import { User } from './User';

/**
 * 家庭模型
 */
export interface Family {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  adminId: string;
  members: FamilyMember[];
  inviteCode: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 家庭成员
 */
export interface FamilyMember {
  id: string;
  userId: string;
  familyId: string;
  role: FamilyRole;
  nickname?: string;
  user?: User;
  joinedAt: string;
}

/**
 * 家庭邀请
 */
export interface FamilyInvitation {
  id: string;
  familyId: string;
  inviterId: string;
  inviteCode: string;
  phone?: string;
  email?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  expiresAt: string;
  createdAt: string;
}

/**
 * 创建家庭参数
 */
export interface CreateFamilyParams {
  name: string;
  description?: string;
  avatar?: string;
}

/**
 * 更新家庭参数
 */
export interface UpdateFamilyParams {
  name?: string;
  description?: string;
  avatar?: string;
}

/**
 * 邀请家庭成员参数
 */
export interface InviteFamilyMemberParams {
  phone?: string;
  email?: string;
  role?: FamilyRole;
  message?: string;
}

/**
 * 加入家庭参数
 */
export interface JoinFamilyParams {
  inviteCode: string;
}

/**
 * 更新成员角色参数
 */
export interface UpdateMemberRoleParams {
  memberId: string;
  role: FamilyRole;
}

/**
 * 家庭统计信息
 */
export interface FamilyStats {
  totalMembers: number;
  activeMembers: number;
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  healthAlerts: number;
}
