import { COLORS, SPACING, FONT_SIZES } from '../../constants/AppConstants';
import { AppCard } from '../../components/ui/Card';
import { AppButton } from '../../components/ui/Button';

/**
 * 日历页面
 */
@Entry
@Component
struct Calendar {
  @State currentDate: Date = new Date();
  @State selectedDate: Date = new Date();
  @State currentTabIndex: number = 0;

  private tabs = ['今日任务', '本月任务', '活动列表'];

  build() {
    Column() {
      // 顶部应用栏
      this.TopAppBar();

      // 日历视图
      this.CalendarView();

      // 标签页
      this.TabsView();

      // 浮动操作按钮
      Stack({ alignContent: Alignment.BottomEnd }) {
        Column() {}
        .layoutWeight(1);

        Button() {
          Image($r('app.media.ic_add'))
            .width(24)
            .height(24)
            .fillColor(Color.White);
        }
        .width(56)
        .height(56)
        .backgroundColor(COLORS.PRIMARY)
        .borderRadius(28)
        .margin({ right: SPACING.MEDIUM, bottom: SPACING.MEDIUM })
        .onClick(() => {
          // TODO: 添加任务/活动
        });
      }
      .layoutWeight(1);
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND);
  }

  @Builder
  TopAppBar() {
    Row() {
      Text('健康日历')
        .fontSize(FONT_SIZES.TITLE)
        .fontWeight(FontWeight.Bold)
        .fontColor(COLORS.TEXT_PRIMARY);

      Blank();

      Image($r('app.media.ic_filter'))
        .width(24)
        .height(24)
        .fillColor(COLORS.TEXT_SECONDARY)
        .onClick(() => {
          // TODO: 打开筛选
        });
    }
    .width('100%')
    .height(60)
    .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM })
    .backgroundColor(COLORS.SURFACE);
  }

  @Builder
  CalendarView() {
    AppCard({
      margin: SPACING.MEDIUM
    }) {
      Column({ space: SPACING.MEDIUM }) {
        // 月份导航
        Row() {
          Image($r('app.media.ic_arrow_left'))
            .width(24)
            .height(24)
            .fillColor(COLORS.TEXT_SECONDARY)
            .onClick(() => {
              this.previousMonth();
            });

          Text(this.getMonthYearText())
            .fontSize(FONT_SIZES.LARGE)
            .fontWeight(FontWeight.Bold)
            .fontColor(COLORS.TEXT_PRIMARY)
            .layoutWeight(1)
            .textAlign(TextAlign.Center);

          Image($r('app.media.ic_arrow_right'))
            .width(24)
            .height(24)
            .fillColor(COLORS.TEXT_SECONDARY)
            .onClick(() => {
              this.nextMonth();
            });
        }
        .width('100%');

        // 星期标题
        Row() {
          ForEach(['日', '一', '二', '三', '四', '五', '六'], (day: string) => {
            Text(day)
              .fontSize(FONT_SIZES.SMALL)
              .fontColor(COLORS.TEXT_SECONDARY)
              .textAlign(TextAlign.Center)
              .layoutWeight(1);
          });
        }
        .width('100%');

        // 日期网格
        this.DateGrid();
      }
      .width('100%');
    };
  }

  @Builder
  DateGrid() {
    Grid() {
      ForEach(this.getCalendarDays(), (day: CalendarDay) => {
        GridItem() {
          Column({ space: 2 }) {
            Text(day.day.toString())
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(day.isCurrentMonth ? COLORS.TEXT_PRIMARY : COLORS.TEXT_DISABLED)
              .fontWeight(day.isToday ? FontWeight.Bold : FontWeight.Normal);

            if (day.hasTask) {
              Circle({ width: 6, height: 6 })
                .fill(COLORS.PRIMARY);
            }
          }
          .width(40)
          .height(40)
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
          .backgroundColor(day.isSelected ? COLORS.PRIMARY : Color.Transparent)
          .borderRadius(20)
          .onClick(() => {
            this.selectedDate = day.date;
          });
        };
      });
    }
    .columnsTemplate('1fr 1fr 1fr 1fr 1fr 1fr 1fr')
    .rowsGap(SPACING.SMALL)
    .height(240);
  }

  @Builder
  TabsView() {
    Column() {
      // 标签栏
      Row() {
        ForEach(this.tabs, (tab: string, index: number) => {
          Text(tab)
            .fontSize(FONT_SIZES.MEDIUM)
            .fontColor(this.currentTabIndex === index ? COLORS.PRIMARY : COLORS.TEXT_SECONDARY)
            .fontWeight(this.currentTabIndex === index ? FontWeight.Medium : FontWeight.Normal)
            .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM, top: SPACING.SMALL, bottom: SPACING.SMALL })
            .onClick(() => {
              this.currentTabIndex = index;
            });
        });
      }
      .width('100%')
      .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM });

      Divider()
        .color(COLORS.BORDER);

      // 内容区域
      Scroll() {
        Column({ space: SPACING.MEDIUM }) {
          this.getTabContent();
        }
        .width('100%')
        .padding(SPACING.MEDIUM);
      }
      .layoutWeight(1);
    }
    .layoutWeight(1);
  }

  @Builder
  getTabContent() {
    switch (this.currentTabIndex) {
      case 0:
        this.TodayTasks();
        break;
      case 1:
        this.MonthTasks();
        break;
      case 2:
        this.ActivityList();
        break;
    }
  }

  @Builder
  TodayTasks() {
    ForEach([
      { title: '服用降压药', time: '08:00', completed: false },
      { title: '血压测量', time: '14:00', completed: true },
      { title: '晚间散步', time: '19:00', completed: false }
    ], (task: any) => {
      AppCard() {
        Row({ space: SPACING.MEDIUM }) {
          Checkbox()
            .select(task.completed)
            .selectedColor(COLORS.PRIMARY);

          Column({ space: 2 }) {
            Text(task.title)
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(COLORS.TEXT_PRIMARY);

            Text(task.time)
              .fontSize(FONT_SIZES.SMALL)
              .fontColor(COLORS.TEXT_SECONDARY);
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start);
        }
        .width('100%')
        .alignItems(VerticalAlign.Center);
      };
    });
  }

  @Builder
  MonthTasks() {
    Text('本月任务列表')
      .fontSize(FONT_SIZES.MEDIUM)
      .fontColor(COLORS.TEXT_SECONDARY);
  }

  @Builder
  ActivityList() {
    Text('活动列表')
      .fontSize(FONT_SIZES.MEDIUM)
      .fontColor(COLORS.TEXT_SECONDARY);
  }

  private getMonthYearText(): string {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth() + 1;
    return `${year}年${month}月`;
  }

  private previousMonth() {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(newDate.getMonth() - 1);
    this.currentDate = newDate;
  }

  private nextMonth() {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(newDate.getMonth() + 1);
    this.currentDate = newDate;
  }

  private getCalendarDays(): CalendarDay[] {
    // TODO: 实现日历日期计算逻辑
    const days: CalendarDay[] = [];
    const today = new Date();
    
    for (let i = 1; i <= 30; i++) {
      const date = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), i);
      days.push({
        day: i,
        date: date,
        isCurrentMonth: true,
        isToday: date.toDateString() === today.toDateString(),
        isSelected: date.toDateString() === this.selectedDate.toDateString(),
        hasTask: Math.random() > 0.7 // 随机显示任务标记
      });
    }
    
    return days;
  }
}

interface CalendarDay {
  day: number;
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  hasTask: boolean;
}
