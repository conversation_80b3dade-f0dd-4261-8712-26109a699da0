import { COLORS, FONT_SIZES, SPACING } from '../../constants/AppConstants';
import { Router } from '../../utils/Router';

/**
 * 底部导航栏组件
 */
@Component
export struct BottomNavigation {
  @State currentIndex: number = 0;
  @Prop tabs: TabItem[] = [
    { id: 'home', title: '首页', icon: 'app.media.ic_home', route: '/pages/home/<USER>' },
    { id: 'archive', title: '档案', icon: 'app.media.ic_archive', route: '/pages/archive/Archive' },
    { id: 'calendar', title: '日历', icon: 'app.media.ic_calendar', route: '/pages/calendar/Calendar' },
    { id: 'assistant', title: '助手', icon: 'app.media.ic_assistant', route: '/pages/assistant/Assistant' },
    { id: 'profile', title: '我的', icon: 'app.media.ic_profile', route: '/pages/profile/Profile' }
  ];

  build() {
    Row() {
      ForEach(this.tabs, (tab: TabItem, index: number) => {
        Column({ space: SPACING.EXTRA_SMALL }) {
          Image($r(tab.icon))
            .width(24)
            .height(24)
            .fillColor(this.currentIndex === index ? COLORS.PRIMARY : COLORS.TEXT_DISABLED);

          Text(tab.title)
            .fontSize(FONT_SIZES.SMALL)
            .fontColor(this.currentIndex === index ? COLORS.PRIMARY : COLORS.TEXT_DISABLED)
            .fontWeight(this.currentIndex === index ? FontWeight.Medium : FontWeight.Normal);
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .padding({ top: SPACING.SMALL, bottom: SPACING.SMALL })
        .onClick(() => {
          if (this.currentIndex !== index) {
            this.currentIndex = index;
            Router.replace(tab.route);
          }
        });
      });
    }
    .width('100%')
    .height(60)
    .backgroundColor(COLORS.SURFACE)
    .border({
      width: { top: 1 },
      color: COLORS.BORDER
    })
    .justifyContent(FlexAlign.SpaceEvenly);
  }
}

/**
 * 标签项接口
 */
export interface TabItem {
  id: string;
  title: string;
  icon: string;
  route: string;
}
