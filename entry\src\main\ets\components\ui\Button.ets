import { COLORS, FONT_SIZES, SPACING, BORDER_RADIUS } from '../../constants/AppConstants';

/**
 * 按钮组件
 */
@Component
export struct AppButton {
  @Prop text: string = '';
  @Prop type: 'primary' | 'secondary' | 'outline' | 'text' = 'primary';
  @Prop size: 'small' | 'medium' | 'large' = 'medium';
  @Prop disabled: boolean = false;
  @Prop loading: boolean = false;
  @Prop fullWidth: boolean = false;
  @Prop icon?: string;
  @Prop iconPosition: 'left' | 'right' = 'left';
  onClick?: () => void;

  private getButtonStyle() {
    const baseStyle = {
      borderRadius: BORDER_RADIUS.MEDIUM,
      justifyContent: FlexAlign.Center,
      alignItems: ItemAlign.Center
    };

    // 尺寸样式
    const sizeStyles = {
      small: {
        height: 32,
        paddingLeft: SPACING.MEDIUM,
        paddingRight: SPACING.MEDIUM,
        fontSize: FONT_SIZES.SMALL
      },
      medium: {
        height: 40,
        paddingLeft: SPACING.LARGE,
        paddingRight: SPACING.LARGE,
        fontSize: FONT_SIZES.MEDIUM
      },
      large: {
        height: 48,
        paddingLeft: SPACING.EXTRA_LARGE,
        paddingRight: SPACING.EXTRA_LARGE,
        fontSize: FONT_SIZES.LARGE
      }
    };

    // 类型样式
    const typeStyles = {
      primary: {
        backgroundColor: this.disabled ? COLORS.TEXT_DISABLED : COLORS.PRIMARY,
        fontColor: Color.White
      },
      secondary: {
        backgroundColor: this.disabled ? COLORS.TEXT_DISABLED : COLORS.SECONDARY,
        fontColor: Color.White
      },
      outline: {
        backgroundColor: Color.Transparent,
        borderWidth: 1,
        borderColor: this.disabled ? COLORS.TEXT_DISABLED : COLORS.PRIMARY,
        fontColor: this.disabled ? COLORS.TEXT_DISABLED : COLORS.PRIMARY
      },
      text: {
        backgroundColor: Color.Transparent,
        fontColor: this.disabled ? COLORS.TEXT_DISABLED : COLORS.PRIMARY
      }
    };

    return {
      ...baseStyle,
      ...sizeStyles[this.size],
      ...typeStyles[this.type],
      width: this.fullWidth ? '100%' : undefined
    };
  }

  build() {
    Button() {
      Row({ space: SPACING.SMALL }) {
        if (this.loading) {
          LoadingProgress()
            .width(16)
            .height(16)
            .color(this.type === 'primary' || this.type === 'secondary' ? Color.White : COLORS.PRIMARY);
        } else if (this.icon && this.iconPosition === 'left') {
          Image($r(this.icon))
            .width(16)
            .height(16)
            .fillColor(this.type === 'primary' || this.type === 'secondary' ? Color.White : COLORS.PRIMARY);
        }

        if (this.text) {
          Text(this.text)
            .fontSize(this.getButtonStyle().fontSize)
            .fontColor(this.getButtonStyle().fontColor)
            .fontWeight(FontWeight.Medium);
        }

        if (this.icon && this.iconPosition === 'right') {
          Image($r(this.icon))
            .width(16)
            .height(16)
            .fillColor(this.type === 'primary' || this.type === 'secondary' ? Color.White : COLORS.PRIMARY);
        }
      }
    }
    .type(ButtonType.Normal)
    .stateEffect(false)
    .backgroundColor(this.getButtonStyle().backgroundColor)
    .borderRadius(this.getButtonStyle().borderRadius)
    .height(this.getButtonStyle().height)
    .width(this.getButtonStyle().width)
    .padding({
      left: this.getButtonStyle().paddingLeft,
      right: this.getButtonStyle().paddingRight
    })
    .border(this.type === 'outline' ? {
      width: this.getButtonStyle().borderWidth,
      color: this.getButtonStyle().borderColor
    } : undefined)
    .enabled(!this.disabled && !this.loading)
    .onClick(() => {
      if (!this.disabled && !this.loading && this.onClick) {
        this.onClick();
      }
    });
  }
}
