import { COLORS, SPACING, FONT_SIZES, BORDER_RADIUS } from '../../constants/AppConstants';
import { AppCard } from '../../components/ui/Card';
import { AppButton } from '../../components/ui/Button';

/**
 * 智能助手页面
 */
@Entry
@Component
struct Assistant {
  @State messages: ChatMessage[] = [
    {
      id: '1',
      content: '您好！我是您的健康助手，有什么可以帮助您的吗？',
      isUser: false,
      timestamp: new Date()
    }
  ];
  @State inputText: string = '';
  @State isLoading: boolean = false;

  private quickQuestions = [
    '我的血压正常吗？',
    '如何改善睡眠质量？',
    '今天的健康任务',
    '家人健康状况'
  ];

  build() {
    Column() {
      // 顶部应用栏
      this.TopAppBar();

      // 聊天内容
      Scroll() {
        Column({ space: SPACING.MEDIUM }) {
          ForEach(this.messages, (message: ChatMessage) => {
            this.MessageItem(message);
          });

          if (this.isLoading) {
            this.LoadingMessage();
          }
        }
        .width('100%')
        .padding(SPACING.MEDIUM);
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off);

      // 快速问题
      if (this.messages.length <= 1) {
        this.QuickQuestions();
      }

      // 输入区域
      this.InputArea();
    }
    .width('100%')
    .height('100%')
    .backgroundColor(COLORS.BACKGROUND);
  }

  @Builder
  TopAppBar() {
    Row() {
      Text('智能助手')
        .fontSize(FONT_SIZES.TITLE)
        .fontWeight(FontWeight.Bold)
        .fontColor(COLORS.TEXT_PRIMARY);

      Blank();

      Image($r('app.media.ic_history'))
        .width(24)
        .height(24)
        .fillColor(COLORS.TEXT_SECONDARY)
        .onClick(() => {
          // TODO: 打开历史对话
        });
    }
    .width('100%')
    .height(60)
    .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM })
    .backgroundColor(COLORS.SURFACE);
  }

  @Builder
  MessageItem(message: ChatMessage) {
    Row() {
      if (message.isUser) {
        Blank();
        
        Column({ space: SPACING.SMALL }) {
          AppCard({
            backgroundColor: COLORS.PRIMARY,
            padding: SPACING.MEDIUM
          }) {
            Text(message.content)
              .fontSize(FONT_SIZES.MEDIUM)
              .fontColor(Color.White);
          };

          Text(this.formatTime(message.timestamp))
            .fontSize(FONT_SIZES.EXTRA_SMALL)
            .fontColor(COLORS.TEXT_DISABLED)
            .alignSelf(ItemAlign.End);
        }
        .alignItems(HorizontalAlign.End)
        .layoutWeight(1);
      } else {
        Row({ space: SPACING.MEDIUM }) {
          Image($r('app.media.ic_assistant_avatar'))
            .width(32)
            .height(32)
            .borderRadius(16);

          Column({ space: SPACING.SMALL }) {
            AppCard({
              backgroundColor: COLORS.SURFACE,
              padding: SPACING.MEDIUM
            }) {
              Text(message.content)
                .fontSize(FONT_SIZES.MEDIUM)
                .fontColor(COLORS.TEXT_PRIMARY);
            };

            Text(this.formatTime(message.timestamp))
              .fontSize(FONT_SIZES.EXTRA_SMALL)
              .fontColor(COLORS.TEXT_DISABLED);
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1);
        }
        .width('100%')
        .alignItems(VerticalAlign.Top);

        Blank();
      }
    }
    .width('100%');
  }

  @Builder
  LoadingMessage() {
    Row({ space: SPACING.MEDIUM }) {
      Image($r('app.media.ic_assistant_avatar'))
        .width(32)
        .height(32)
        .borderRadius(16);

      AppCard({
        backgroundColor: COLORS.SURFACE,
        padding: SPACING.MEDIUM
      }) {
        Row({ space: SPACING.SMALL }) {
          LoadingProgress()
            .width(16)
            .height(16)
            .color(COLORS.PRIMARY);

          Text('正在思考中...')
            .fontSize(FONT_SIZES.MEDIUM)
            .fontColor(COLORS.TEXT_SECONDARY);
        };
      };

      Blank();
    }
    .width('100%')
    .alignItems(VerticalAlign.Top);
  }

  @Builder
  QuickQuestions() {
    Column({ space: SPACING.MEDIUM }) {
      Text('快速咨询')
        .fontSize(FONT_SIZES.MEDIUM)
        .fontWeight(FontWeight.Medium)
        .fontColor(COLORS.TEXT_PRIMARY)
        .alignSelf(ItemAlign.Start)
        .margin({ left: SPACING.MEDIUM });

      Scroll() {
        Row({ space: SPACING.SMALL }) {
          ForEach(this.quickQuestions, (question: string) => {
            AppButton({
              text: question,
              type: 'outline',
              size: 'small',
              onClick: () => {
                this.sendMessage(question);
              }
            });
          });
        }
        .padding({ left: SPACING.MEDIUM, right: SPACING.MEDIUM });
      }
      .scrollable(ScrollDirection.Horizontal)
      .scrollBar(BarState.Off);
    }
    .width('100%')
    .margin({ bottom: SPACING.MEDIUM });
  }

  @Builder
  InputArea() {
    Column() {
      Divider()
        .color(COLORS.BORDER);

      Row({ space: SPACING.MEDIUM }) {
        TextInput({ placeholder: '请输入您的问题...' })
          .layoutWeight(1)
          .height(40)
          .borderRadius(BORDER_RADIUS.LARGE)
          .backgroundColor(COLORS.BACKGROUND)
          .fontSize(FONT_SIZES.MEDIUM)
          .onChange((value: string) => {
            this.inputText = value;
          });

        Image($r('app.media.ic_microphone'))
          .width(24)
          .height(24)
          .fillColor(COLORS.TEXT_SECONDARY)
          .onClick(() => {
            // TODO: 语音输入
          });

        Button() {
          Image($r('app.media.ic_send'))
            .width(20)
            .height(20)
            .fillColor(Color.White);
        }
        .width(40)
        .height(40)
        .backgroundColor(this.inputText.trim() ? COLORS.PRIMARY : COLORS.TEXT_DISABLED)
        .borderRadius(20)
        .enabled(this.inputText.trim().length > 0)
        .onClick(() => {
          if (this.inputText.trim()) {
            this.sendMessage(this.inputText.trim());
            this.inputText = '';
          }
        });
      }
      .width('100%')
      .padding(SPACING.MEDIUM)
      .backgroundColor(COLORS.SURFACE);
    }
    .width('100%');
  }

  private sendMessage(content: string) {
    // 添加用户消息
    this.messages.push({
      id: Date.now().toString(),
      content: content,
      isUser: true,
      timestamp: new Date()
    });

    // 模拟AI回复
    this.isLoading = true;
    setTimeout(() => {
      this.isLoading = false;
      this.messages.push({
        id: (Date.now() + 1).toString(),
        content: this.getAIResponse(content),
        isUser: false,
        timestamp: new Date()
      });
    }, 2000);
  }

  private getAIResponse(question: string): string {
    // 简单的模拟回复
    if (question.includes('血压')) {
      return '根据您最近的血压记录，您的血压值在正常范围内。建议继续保持良好的生活习惯，定期监测。';
    } else if (question.includes('睡眠')) {
      return '改善睡眠质量的建议：1. 保持规律作息 2. 睡前避免使用电子设备 3. 创造舒适的睡眠环境 4. 适量运动';
    } else if (question.includes('任务')) {
      return '今天您有3个健康任务：服用降压药(已完成)、血压测量(待完成)、晚间散步(待完成)。';
    } else {
      return '感谢您的咨询，我会根据您的健康数据为您提供个性化的建议。如需更详细的分析，请提供更多信息。';
    }
  }

  private formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}
