/**
 * 通用类型定义
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

// 分页参数
export interface PageParams {
  page: number;
  pageSize: number;
}

// 分页响应
export interface PageResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 性别枚举
export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other'
}

// 血型枚举
export enum BloodType {
  A = 'A',
  B = 'B',
  AB = 'AB',
  O = 'O',
  UNKNOWN = 'unknown'
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 任务类型枚举
export enum TaskType {
  MEDICATION = 'medication',
  HEALTH_CHECK = 'health_check',
  DOCTOR_VISIT = 'doctor_visit',
  EXERCISE = 'exercise',
  DIET = 'diet',
  OTHER = 'other'
}

// 优先级枚举
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 健康状态枚举
export enum HealthStatus {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
  CRITICAL = 'critical'
}

// 文件类型枚举
export enum FileType {
  IMAGE = 'image',
  PDF = 'pdf',
  DOC = 'doc',
  OTHER = 'other'
}

// 设备类型枚举
export enum DeviceType {
  BLOOD_PRESSURE = 'blood_pressure',
  BLOOD_GLUCOSE = 'blood_glucose',
  HEART_RATE = 'heart_rate',
  WEIGHT_SCALE = 'weight_scale',
  THERMOMETER = 'thermometer',
  OTHER = 'other'
}

// 通知类型枚举
export enum NotificationType {
  TASK_REMINDER = 'task_reminder',
  HEALTH_ALERT = 'health_alert',
  EMERGENCY = 'emergency',
  SYSTEM = 'system'
}

// 家庭成员角色枚举
export enum FamilyRole {
  ADMIN = 'admin',
  MEMBER = 'member',
  CHILD = 'child',
  ELDERLY = 'elderly'
}
