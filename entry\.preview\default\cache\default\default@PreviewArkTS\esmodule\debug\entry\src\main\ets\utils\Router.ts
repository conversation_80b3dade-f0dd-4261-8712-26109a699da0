import router from "@ohos:router";
import { ROUTES } from "@normalized:N&&&entry/src/main/ets/constants/AppConstants&";
/**
 * 路由工具类
 */
export class Router {
    /**
     * 跳转到指定页面
     */
    static push(url: string, params?: Record<string, any>): void {
        router.pushUrl({
            url,
            params
        }).catch((error: Error) => {
            console.error('Router push error:', error);
        });
    }
    /**
     * 替换当前页面
     */
    static replace(url: string, params?: Record<string, any>): void {
        router.replaceUrl({
            url,
            params
        }).catch((error: Error) => {
            console.error('Router replace error:', error);
        });
    }
    /**
     * 返回上一页
     */
    static back(): void {
        router.back();
    }
    /**
     * 清空路由栈并跳转
     */
    static clear(url: string, params?: Record<string, any>): void {
        router.clear();
        this.push(url, params);
    }
    /**
     * 获取路由参数
     */
    static getParams(): Record<string, any> {
        return router.getParams() as Record<string, any>;
    }
    // 便捷方法
    static toLogin(): void {
        this.replace(ROUTES.LOGIN);
    }
    static toHome(): void {
        this.replace(ROUTES.HOME);
    }
    static toOnboarding(): void {
        this.push(ROUTES.ONBOARDING);
    }
    static toFamilySetup(): void {
        this.push(ROUTES.FAMILY_SETUP);
    }
    static toPersonalInfo(userId?: string): void {
        this.push(ROUTES.PERSONAL_INFO, { userId });
    }
    static toHealthCenter(): void {
        this.push(ROUTES.HEALTH_CENTER);
    }
    static toTaskDetail(taskId: string): void {
        this.push(ROUTES.TASK_DETAIL, { taskId });
    }
    static toHealthRecordDetail(recordId: string): void {
        this.push(ROUTES.HEALTH_RECORD_DETAIL, { recordId });
    }
    static toSettings(): void {
        this.push(ROUTES.SETTINGS);
    }
    static toFamilyManagement(): void {
        this.push(ROUTES.FAMILY_MANAGEMENT);
    }
}
