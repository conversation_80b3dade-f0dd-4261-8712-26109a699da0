import { Router } from '../utils/Router';
import { COLORS } from '../constants/AppConstants';

@Entry
@Component
struct Index {
  aboutToAppear() {
    // 检查用户登录状态
    this.checkLoginStatus();
  }

  build() {
    Column() {
      // 启动画面
      Image($r('app.media.app_logo'))
        .width(120)
        .height(120);

      Text('家庭健康管理')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .fontColor(COLORS.TEXT_PRIMARY)
        .margin({ top: 20 });

      Text('守护家人健康，从这里开始')
        .fontSize(16)
        .fontColor(COLORS.TEXT_SECONDARY)
        .margin({ top: 10 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor(COLORS.BACKGROUND);
  }

  private checkLoginStatus() {
    // 模拟检查登录状态
    setTimeout(() => {
      // TODO: 检查本地存储的登录信息
      const isLoggedIn = false; // 这里应该从本地存储读取

      if (isLoggedIn) {
        Router.replace('/pages/MainTabs');
      } else {
        Router.replace('/pages/auth/Onboarding');
      }
    }, 2000);
  }
}