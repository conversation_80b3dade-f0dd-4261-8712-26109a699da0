import { TaskStatus, TaskType, Priority } from '../types/CommonTypes';

/**
 * 健康任务
 */
export interface HealthTask {
  id: string;
  userId: string;
  familyId: string;
  title: string;
  description?: string;
  type: TaskType;
  status: TaskStatus;
  priority: Priority;
  dueDate: string;
  dueTime?: string;
  isRecurring: boolean;
  recurringPattern?: RecurringPattern;
  reminderSettings?: ReminderSettings;
  completedAt?: string;
  completedBy?: string;
  notes?: string;
  attachments?: TaskAttachment[];
  createdAt: string;
  updatedAt: string;
}

/**
 * 重复模式
 */
export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // 间隔，如每2天、每3周
  daysOfWeek?: number[]; // 周几，0-6表示周日到周六
  dayOfMonth?: number; // 每月第几天
  endDate?: string; // 结束日期
  occurrences?: number; // 重复次数
}

/**
 * 提醒设置
 */
export interface ReminderSettings {
  enabled: boolean;
  advanceMinutes: number[]; // 提前多少分钟提醒，如[15, 60]表示提前15分钟和1小时
  sound: boolean;
  vibration: boolean;
  customMessage?: string;
}

/**
 * 任务附件
 */
export interface TaskAttachment {
  id: string;
  name: string;
  type: string;
  url: string;
  size: number;
  uploadedAt: string;
}

/**
 * 健康活动
 */
export interface HealthActivity {
  id: string;
  userId: string;
  familyId: string;
  title: string;
  description?: string;
  type: ActivityType;
  status: TaskStatus;
  startDate: string;
  endDate?: string;
  startTime?: string;
  endTime?: string;
  location?: string;
  participants?: string[]; // 参与者用户ID
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 活动类型
 */
export enum ActivityType {
  EXERCISE = 'exercise',
  MEDICAL_APPOINTMENT = 'medical_appointment',
  HEALTH_CHECKUP = 'health_checkup',
  THERAPY = 'therapy',
  WELLNESS = 'wellness',
  OTHER = 'other'
}

/**
 * 创建任务参数
 */
export interface CreateTaskParams {
  title: string;
  description?: string;
  type: TaskType;
  priority?: Priority;
  dueDate: string;
  dueTime?: string;
  isRecurring?: boolean;
  recurringPattern?: RecurringPattern;
  reminderSettings?: ReminderSettings;
  userId?: string; // 如果不指定，默认为当前用户
}

/**
 * 更新任务参数
 */
export interface UpdateTaskParams {
  title?: string;
  description?: string;
  type?: TaskType;
  priority?: Priority;
  dueDate?: string;
  dueTime?: string;
  status?: TaskStatus;
  notes?: string;
}

/**
 * 任务查询参数
 */
export interface TaskQuery {
  userId?: string;
  familyId?: string;
  type?: TaskType;
  status?: TaskStatus;
  priority?: Priority;
  startDate?: string;
  endDate?: string;
  keyword?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 任务统计
 */
export interface TaskStats {
  total: number;
  pending: number;
  inProgress: number;
  completed: number;
  cancelled: number;
  overdue: number;
}
